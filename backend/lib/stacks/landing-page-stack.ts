import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53targets from 'aws-cdk-lib/aws-route53-targets';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import * as s3deploy from 'aws-cdk-lib/aws-s3-deployment';
import { Construct } from 'constructs';

export interface LandingPageStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  isProductionOrStaging: boolean;
  certificateArn?: string;
}

export class LandingPageStack extends cdk.NestedStack {
  public readonly bucket: s3.Bucket;
  public readonly distribution: cloudfront.Distribution;
  public readonly bucketName: string;
  public readonly distributionId: string;
  public readonly distributionDomainName: string;
  public readonly distributionUrl: string;

  constructor(scope: Construct, id: string, props: LandingPageStackProps) {
    super(scope, id, props);

    const { projectName, environment, isProductionOrStaging, certificateArn } = props;

    // Create S3 bucket for static website hosting
    this.bucket = this.createStaticWebsiteBucket(projectName, environment, isProductionOrStaging);

    // Set public properties
    this.bucketName = this.bucket.bucketName;

    // Create CloudFront distribution
    this.distribution = this.createCloudFrontDistribution(
      projectName,
      environment,
      isProductionOrStaging,
      certificateArn
    );

    // Set distribution properties
    this.distributionId = this.distribution.distributionId;
    this.distributionDomainName = this.distribution.distributionDomainName;
    this.distributionUrl = `https://${this.distributionDomainName}`;

    // Create Route53 records for gameflex.io domain
    this.createRoute53Records();

    // Deploy static website files
    this.deployStaticFiles();

    // Create outputs
    this.createOutputs();
  }

  private createStaticWebsiteBucket(
    projectName: string,
    environment: string,
    isProductionOrStaging: boolean
  ): s3.Bucket {
    const bucketName = `${projectName}-landing-page-${environment}`;

    const bucket = new s3.Bucket(this, 'LandingPageBucket', {
      bucketName,
      publicReadAccess: false, // We'll use CloudFront for public access
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      encryption: s3.BucketEncryption.S3_MANAGED,
      removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: !isProductionOrStaging,
      versioned: isProductionOrStaging,
      cors: [
        {
          allowedMethods: [s3.HttpMethods.GET, s3.HttpMethods.HEAD],
          allowedOrigins: ['*'],
          allowedHeaders: ['*'],
          maxAge: 3000,
        },
      ],
    });

    return bucket;
  }

  private createCloudFrontDistribution(
    projectName: string,
    environment: string,
    isProductionOrStaging: boolean,
    certificateArn?: string
  ): cloudfront.Distribution {
    // Create Origin Access Control for S3
    const originAccessControl = new cloudfront.S3OriginAccessControl(this, 'LandingPageOAC', {
      description: `OAC for ${projectName} Landing Page - ${environment}`,
    });

    // S3 origin
    const s3Origin = origins.S3BucketOrigin.withOriginAccessControl(this.bucket, {
      originAccessControl,
    });

    // Default cache behavior for static website
    const defaultCacheBehavior: cloudfront.BehaviorOptions = {
      origin: s3Origin,
      viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
      allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
      cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
      compress: true,
      cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
      originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
    };

    // Additional cache behaviors for different file types
    const additionalBehaviors: Record<string, cloudfront.BehaviorOptions> = {
      // Static assets with longer cache
      '*.css': {
        origin: s3Origin,
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
        compress: true,
        cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
        originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
      },
      '*.js': {
        origin: s3Origin,
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
        compress: true,
        cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
        originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
      },
      // Images with longer cache
      '*.png': {
        origin: s3Origin,
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
        compress: true,
        cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED_FOR_UNCOMPRESSED_OBJECTS,
        originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
      },
      '*.jpg': {
        origin: s3Origin,
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
        compress: true,
        cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED_FOR_UNCOMPRESSED_OBJECTS,
        originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
      },
      '*.svg': {
        origin: s3Origin,
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
        compress: true,
        cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
        originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
      },
    };

    // Certificate configuration for custom domain
    let domainNames: string[] | undefined;
    let certificate: acm.ICertificate | undefined;

    if (certificateArn) {
      // Import existing certificate
      certificate = acm.Certificate.fromCertificateArn(this, 'LandingPageCertificate', certificateArn);

      // Set domain names based on environment
      if (environment === 'production') {
        domainNames = ['gameflex.io'];
      } else {
        domainNames = [`${environment}.gameflex.io`];
      }
    }

    const distribution = new cloudfront.Distribution(this, 'LandingPageDistribution', {
      comment: `${projectName} Landing Page CDN - ${environment}`,
      defaultBehavior: defaultCacheBehavior,
      additionalBehaviors,
      domainNames,
      certificate,
      priceClass: isProductionOrStaging
        ? cloudfront.PriceClass.PRICE_CLASS_ALL
        : cloudfront.PriceClass.PRICE_CLASS_100,
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2_AND_3,
      minimumProtocolVersion: cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
      defaultRootObject: 'index.html',
      errorResponses: [
        {
          httpStatus: 403,
          responseHttpStatus: 404,
          responsePagePath: '/404.html',
          ttl: cdk.Duration.minutes(5),
        },
        {
          httpStatus: 404,
          responseHttpStatus: 404,
          responsePagePath: '/404.html',
          ttl: cdk.Duration.minutes(5),
        },
      ],
      enableLogging: isProductionOrStaging,
      logBucket: isProductionOrStaging ? this.createLogsBucket(projectName, environment) : undefined,
      logFilePrefix: 'cloudfront-logs/',
      logIncludesCookies: false,
    });

    return distribution;
  }



  private createLogsBucket(projectName: string, environment: string): s3.Bucket {
    return new s3.Bucket(this, 'LandingPageLogsBucket', {
      bucketName: `${projectName}-landing-page-logs-${environment}`,
      publicReadAccess: false,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      encryption: s3.BucketEncryption.S3_MANAGED,
      objectOwnership: s3.ObjectOwnership.BUCKET_OWNER_PREFERRED, // Enable ACLs for CloudFront logging
      lifecycleRules: [
        {
          id: 'delete-old-logs',
          enabled: true,
          expiration: cdk.Duration.days(90),
        },
      ],
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: true,
    });
  }

  private createRoute53Records(): void {
    // Import existing hosted zone for gameflex.io
    const hostedZone = route53.HostedZone.fromLookup(this, 'GameFlexHostedZone', {
      domainName: 'gameflex.io',
    });

    // Create A record for root domain (production only)
    if (this.node.tryGetContext('environment') === 'production') {
      new route53.ARecord(this, 'LandingPageARecord', {
        zone: hostedZone,
        recordName: 'gameflex.io',
        target: route53.RecordTarget.fromAlias(
          new route53targets.CloudFrontTarget(this.distribution)
        ),
        comment: 'GameFlex Landing Page - Root Domain',
      });

      // Create AAAA record for IPv6 (production only)
      new route53.AaaaRecord(this, 'LandingPageAAAARecord', {
        zone: hostedZone,
        recordName: 'gameflex.io',
        target: route53.RecordTarget.fromAlias(
          new route53targets.CloudFrontTarget(this.distribution)
        ),
        comment: 'GameFlex Landing Page - Root Domain IPv6',
      });
    } else {
      // For non-production environments, create subdomain records
      const environment = this.node.tryGetContext('environment') || 'development';

      new route53.ARecord(this, 'LandingPageARecord', {
        zone: hostedZone,
        recordName: `${environment}.gameflex.io`,
        target: route53.RecordTarget.fromAlias(
          new route53targets.CloudFrontTarget(this.distribution)
        ),
        comment: `GameFlex Landing Page - ${environment} Environment`,
      });

      new route53.AaaaRecord(this, 'LandingPageAAAARecord', {
        zone: hostedZone,
        recordName: `${environment}.gameflex.io`,
        target: route53.RecordTarget.fromAlias(
          new route53targets.CloudFrontTarget(this.distribution)
        ),
        comment: `GameFlex Landing Page - ${environment} Environment IPv6`,
      });
    }
  }

  private deployStaticFiles(): void {
    // Deploy the static website files from the backend/static-website directory
    new s3deploy.BucketDeployment(this, 'LandingPageDeployment', {
      sources: [s3deploy.Source.asset('./static-website')],
      destinationBucket: this.bucket,
      distribution: this.distribution,
      distributionPaths: ['/*'],
      prune: true,
      retainOnDelete: false,
      memoryLimit: 512,
      ephemeralStorageSize: cdk.Size.mebibytes(1024),
    });
  }

  private createOutputs(): void {
    // S3 Bucket outputs
    new cdk.CfnOutput(this, 'LandingPageBucketName', {
      value: this.bucketName,
      description: 'Name of the S3 bucket hosting the landing page',
      exportName: `${this.stackName}-LandingPageBucketName`,
    });

    new cdk.CfnOutput(this, 'LandingPageBucketArn', {
      value: this.bucket.bucketArn,
      description: 'ARN of the S3 bucket hosting the landing page',
      exportName: `${this.stackName}-LandingPageBucketArn`,
    });

    // CloudFront outputs
    new cdk.CfnOutput(this, 'LandingPageDistributionId', {
      value: this.distributionId,
      description: 'CloudFront Distribution ID for the landing page',
      exportName: `${this.stackName}-LandingPageDistributionId`,
    });

    new cdk.CfnOutput(this, 'LandingPageDistributionDomainName', {
      value: this.distributionDomainName,
      description: 'CloudFront Distribution Domain Name for the landing page',
      exportName: `${this.stackName}-LandingPageDistributionDomainName`,
    });

    new cdk.CfnOutput(this, 'LandingPageUrl', {
      value: this.distributionUrl,
      description: 'URL of the landing page',
      exportName: `${this.stackName}-LandingPageUrl`,
    });

    // Custom domain output (if applicable)
    const environment = this.node.tryGetContext('environment') || 'development';
    let customDomainUrl: string;

    if (environment === 'production') {
      customDomainUrl = 'https://gameflex.io';
    } else {
      customDomainUrl = `https://${environment}.gameflex.io`;
    }

    new cdk.CfnOutput(this, 'LandingPageCustomDomainUrl', {
      value: customDomainUrl,
      description: 'Custom domain URL of the landing page',
      exportName: `${this.stackName}-LandingPageCustomDomainUrl`,
    });
  }
}
