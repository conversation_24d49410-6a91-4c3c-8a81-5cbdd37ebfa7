import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';  // Temporarily disabled
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/notification_model.dart';
import '../services/api_service.dart';
import '../utils/app_logger.dart';

// Notification service with stub implementation for Firebase messaging
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static NotificationService get instance => _instance;

  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  String? _fcmToken;
  String? _deviceId;
  bool _isInitialized = false;

  // Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('NotificationService: Initializing notification service');

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Get device info
      await _getDeviceInfo();

      // For now, skip Firebase initialization since it's disabled
      // When Firebase is re-enabled, uncomment the following:
      // await _initializeFirebaseMessaging();
      // await _requestPermissions();
      // await _getFCMToken();
      // await _registerDeviceToken();
      // _setupMessageHandlers();

      _isInitialized = true;
      AppLogger.info('NotificationService: Initialization complete');
    } catch (e) {
      AppLogger.error('NotificationService: Failed to initialize: $e');
    }
  }

  // Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  // Get device information
  Future<void> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        _deviceId = iosInfo.identifierForVendor;
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        _deviceId = androidInfo.id;
      }
      AppLogger.debug('NotificationService: Device ID: $_deviceId');
    } catch (e) {
      AppLogger.error('NotificationService: Failed to get device info: $e');
    }
  }

  // Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    AppLogger.info('NotificationService: Notification tapped');

    // Parse payload if available
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        _handleNotificationNavigation(data);
      } catch (e) {
        AppLogger.error(
          'NotificationService: Error parsing notification payload: $e',
        );
      }
    }
  }

  // Handle notification navigation
  void _handleNotificationNavigation(Map<String, dynamic> data) {
    final type = data['type'] as String?;

    AppLogger.info('NotificationService: Navigate to $type with data: $data');

    // TODO: Implement actual navigation logic based on your app's routing
    // This would typically use your app's navigation service
  }

  // Show local notification
  Future<void> showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'gameflex_notifications',
      'GameFlex Notifications',
      channelDescription: 'Notifications from GameFlex',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    final payload = data != null ? jsonEncode(data) : null;

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      details,
      payload: payload,
    );
  }

  // Request notification permissions (stub for now)
  Future<bool> requestPermissions() async {
    AppLogger.info('NotificationService: Permissions requested');
    // When Firebase is re-enabled, implement actual permission request
    return true;
  }

  // Register device token with backend (stub for now)
  Future<void> _registerDeviceToken() async {
    if (_fcmToken == null || _deviceId == null) {
      AppLogger.warning(
        'NotificationService: Missing token or device ID for registration',
      );
      return;
    }

    try {
      // This would register the device token with your backend
      AppLogger.info('NotificationService: Device token registered (stub)');
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error registering device token: $e',
      );
    }
  }

  // API methods for notification management
  Future<List<NotificationModel>> getNotifications({
    int limit = 20,
    String? lastKey,
  }) async {
    try {
      final queryParams = <String, String>{'limit': limit.toString()};

      if (lastKey != null) {
        queryParams['lastKey'] = lastKey;
      }

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/notifications',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final notifications =
            (data['notifications'] as List)
                .map((json) => NotificationModel.fromJson(json))
                .toList();

        return notifications;
      } else {
        throw Exception('Failed to get notifications: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('NotificationService: Error getting notifications: $e');
      rethrow;
    }
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'PUT',
        path: '/notifications/$notificationId/read',
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Failed to mark notification as read: ${response.statusCode}',
        );
      }
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error marking notification as read: $e',
      );
      rethrow;
    }
  }

  Future<void> markAllNotificationsAsRead() async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'PUT',
        path: '/notifications/read-all',
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Failed to mark all notifications as read: ${response.statusCode}',
        );
      }
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error marking all notifications as read: $e',
      );
      rethrow;
    }
  }

  Future<NotificationPreferences> getNotificationPreferences() async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/notification-preferences',
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return NotificationPreferences.fromJson(data);
      } else {
        throw Exception(
          'Failed to get notification preferences: ${response.statusCode}',
        );
      }
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error getting notification preferences: $e',
      );
      rethrow;
    }
  }

  Future<void> updateNotificationPreferences(
    NotificationPreferences preferences,
  ) async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'PUT',
        path: '/notification-preferences',
        body: preferences.toJson(),
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Failed to update notification preferences: ${response.statusCode}',
        );
      }
    } catch (e) {
      AppLogger.error(
        'NotificationService: Error updating notification preferences: $e',
      );
      rethrow;
    }
  }

  // Getters
  String? get fcmToken => _fcmToken;
  String? get deviceId => _deviceId;
  bool get isInitialized => _isInitialized;
}
